from fastapi import APIRouter, Query, HTTPException, Depends
from typing import List, Optional
import logging
from datetime import datetime

from app.models import (
    ClinicalDataQuery, StatisticsQuery, ClinicalDataResponse,
    Statistics, DiagnosisTrends, DoctorActivity, DiagnosisByAge,
    Doctor, Unit, ApiResponse, HealthCheck, GenderEnum, AgeGroupEnum,
    SymptomCluster
)
from app.services.clinical_service import ClinicalService
from app.services.analytics_service import AnalyticsService
from app.database import db

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/clinical", tags=["clinical"])

@router.get("/health", response_model=ApiResponse)
async def health_check():
    """Health check endpoint"""
    try:
        db_status = db.test_connection()
        health_data = HealthCheck(
            status="healthy" if db_status else "unhealthy",
            database=db_status,
            timestamp=datetime.now().isoformat()
        )
        
        return ApiResponse(
            success=True,
            data=health_data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

@router.get("/data", response_model=ApiResponse)
async def get_clinical_data(
    page: Optional[int] = Query(1, ge=1, description="Page number"),
    limit: Optional[int] = Query(100, ge=1, le=1000, description="Items per page"),
    startDate: Optional[str] = Query(None, description="Start date (ISO string)"),
    endDate: Optional[str] = Query(None, description="End date (ISO string)"),
    doctorId: Optional[str] = Query(None, description="Doctor ID"),
    diagnosis: Optional[str] = Query(None, description="Diagnosis filter"),
    ageGroup: Optional[AgeGroupEnum] = Query(None, description="Age group filter"),
    gender: Optional[GenderEnum] = Query(None, description="Gender filter"),
    unitId: Optional[str] = Query(None, description="Unit ID")
):
    """Get clinical data with optional filtering and pagination"""
    try:
        query = ClinicalDataQuery(
            page=page,
            limit=limit,
            startDate=startDate,
            endDate=endDate,
            doctorId=doctorId,
            diagnosis=diagnosis,
            ageGroup=ageGroup,
            gender=gender,
            unitId=unitId
        )
        
        result = ClinicalService.get_clinical_data(query)
        
        return ApiResponse(
            success=True,
            data=result,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error fetching clinical data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/statistics", response_model=ApiResponse)
async def get_statistics(
    startDate: Optional[str] = Query(None, description="Start date (ISO string)"),
    endDate: Optional[str] = Query(None, description="End date (ISO string)"),
    doctorId: Optional[str] = Query(None, description="Doctor ID"),
    unitId: Optional[str] = Query(None, description="Unit ID")
):
    """Get clinical statistics and summary data"""
    try:
        query = StatisticsQuery(
            startDate=startDate,
            endDate=endDate,
            doctorId=doctorId,
            unitId=unitId
        )
        
        result = ClinicalService.get_statistics(query)
        
        return ApiResponse(
            success=True,
            data=result,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error fetching statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/diagnosis-trends", response_model=ApiResponse)
async def get_diagnosis_trends(
    startDate: Optional[str] = Query(None, description="Start date (ISO string)"),
    endDate: Optional[str] = Query(None, description="End date (ISO string)"),
    doctorId: Optional[str] = Query(None, description="Doctor ID"),
    unitId: Optional[str] = Query(None, description="Unit ID")
):
    """Get diagnosis trends over time"""
    try:
        query = StatisticsQuery(
            startDate=startDate,
            endDate=endDate,
            doctorId=doctorId,
            unitId=unitId
        )
        
        result = ClinicalService.get_diagnosis_trends(query)
        
        return ApiResponse(
            success=True,
            data=result,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error fetching diagnosis trends: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/doctor-activity", response_model=ApiResponse)
async def get_doctor_activity(
    startDate: Optional[str] = Query(None, description="Start date (ISO string)"),
    endDate: Optional[str] = Query(None, description="End date (ISO string)"),
    doctorId: Optional[str] = Query(None, description="Doctor ID"),
    unitId: Optional[str] = Query(None, description="Unit ID")
):
    """Get doctor activity and performance metrics"""
    try:
        query = StatisticsQuery(
            startDate=startDate,
            endDate=endDate,
            doctorId=doctorId,
            unitId=unitId
        )
        
        result = ClinicalService.get_doctor_activity(query)
        
        return ApiResponse(
            success=True,
            data=result,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error fetching doctor activity: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/diagnosis-by-age", response_model=ApiResponse)
async def get_diagnosis_by_age(
    startDate: Optional[str] = Query(None, description="Start date (ISO string)"),
    endDate: Optional[str] = Query(None, description="End date (ISO string)"),
    doctorId: Optional[str] = Query(None, description="Doctor ID"),
    unitId: Optional[str] = Query(None, description="Unit ID")
):
    """Get diagnosis distribution by age groups"""
    try:
        query = StatisticsQuery(
            startDate=startDate,
            endDate=endDate,
            doctorId=doctorId,
            unitId=unitId
        )
        
        result = ClinicalService.get_diagnosis_by_age(query)
        
        return ApiResponse(
            success=True,
            data=result,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error fetching diagnosis by age: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/doctors", response_model=ApiResponse)
async def get_doctors():
    """Get list of all doctors"""
    try:
        result = ClinicalService.get_doctors()
        
        return ApiResponse(
            success=True,
            data=result,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error fetching doctors: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/units", response_model=ApiResponse)
async def get_units():
    """Get list of all units"""
    try:
        result = ClinicalService.get_units()

        return ApiResponse(
            success=True,
            data=result,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error fetching units: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/symptom-clusters", response_model=ApiResponse)
async def get_symptom_clusters():
    """Get symptom clusters and probable diagnoses"""
    try:
        result = AnalyticsService.get_symptom_clusters()

        return ApiResponse(
            success=True,
            data=result,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error fetching symptom clusters: {e}")
        raise HTTPException(status_code=500, detail=str(e))
