from typing import List, Dict, Set
import logging
from collections import defaultdict
from app.models import SymptomCluster, ClinicalDataQuery
from app.services.clinical_service import ClinicalService

logger = logging.getLogger(__name__)

class AnalyticsService:
    
    @staticmethod
    def get_symptom_clusters() -> List[SymptomCluster]:
        """Generate symptom clusters and probable diagnoses"""
        try:
            # Get clinical data for analysis
            query = ClinicalDataQuery(limit=10000)
            clinical_data = ClinicalService.get_clinical_data(query)
            visits = clinical_data.visits
            
            # Group symptoms by diagnosis
            diagnosis_symptom_map: Dict[str, Set[str]] = defaultdict(set)
            
            for visit in visits:
                if visit.diagnosis and visit.chiefComplaint:
                    diagnosis_symptom_map[visit.diagnosis].add(visit.chiefComplaint)
            
            # Create symptom clusters based on common patterns
            clusters = []
            
            # Respiratory symptoms cluster
            respiratory_symptoms = set()
            respiratory_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'cough', 'breath', 'chest', 'fever', 'cold', 'flu', 
                        'respiratory', 'pneumonia', 'bronchitis', 'asthma'
                    ]):
                        respiratory_symptoms.add(symptom)
                        respiratory_diagnoses.add(diagnosis)
            
            if respiratory_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Respiratory Symptoms",
                    symptoms=list(respiratory_symptoms)[:10],  # Limit to 10 symptoms
                    probableDiagnosis=', '.join(list(respiratory_diagnoses)[:5])  # Limit to 5 diagnoses
                ))
            
            # Cardiovascular symptoms cluster
            cardio_symptoms = set()
            cardio_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'chest pain', 'heart', 'palpitation', 'hypertension', 
                        'blood pressure', 'cardiac', 'angina', 'arrhythmia'
                    ]):
                        cardio_symptoms.add(symptom)
                        cardio_diagnoses.add(diagnosis)
            
            if cardio_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Cardiovascular Symptoms",
                    symptoms=list(cardio_symptoms)[:10],
                    probableDiagnosis=', '.join(list(cardio_diagnoses)[:5])
                ))
            
            # Gastrointestinal symptoms cluster
            gi_symptoms = set()
            gi_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'abdominal', 'stomach', 'nausea', 'vomit', 'diarrhea', 
                        'constipation', 'gastro', 'bowel', 'digestive'
                    ]):
                        gi_symptoms.add(symptom)
                        gi_diagnoses.add(diagnosis)
            
            if gi_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Gastrointestinal Symptoms",
                    symptoms=list(gi_symptoms)[:10],
                    probableDiagnosis=', '.join(list(gi_diagnoses)[:5])
                ))
            
            # Neurological symptoms cluster
            neuro_symptoms = set()
            neuro_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'headache', 'dizz', 'memory', 'vision', 'neurolog', 
                        'migraine', 'seizure', 'stroke', 'confusion'
                    ]):
                        neuro_symptoms.add(symptom)
                        neuro_diagnoses.add(diagnosis)
            
            if neuro_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Neurological Symptoms",
                    symptoms=list(neuro_symptoms)[:10],
                    probableDiagnosis=', '.join(list(neuro_diagnoses)[:5])
                ))
            
            # Musculoskeletal symptoms cluster
            musculo_symptoms = set()
            musculo_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'pain', 'back', 'joint', 'muscle', 'arthritis', 
                        'fracture', 'sprain', 'orthopedic', 'bone'
                    ]):
                        musculo_symptoms.add(symptom)
                        musculo_diagnoses.add(diagnosis)
            
            if musculo_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Musculoskeletal Symptoms",
                    symptoms=list(musculo_symptoms)[:10],
                    probableDiagnosis=', '.join(list(musculo_diagnoses)[:5])
                ))
            
            # Metabolic symptoms cluster
            meta_symptoms = set()
            meta_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'diabetes', 'blood sugar', 'thyroid', 'weight', 
                        'metabolic', 'hormone', 'endocrine', 'fatigue'
                    ]):
                        meta_symptoms.add(symptom)
                        meta_diagnoses.add(diagnosis)
            
            if meta_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Metabolic Symptoms",
                    symptoms=list(meta_symptoms)[:10],
                    probableDiagnosis=', '.join(list(meta_diagnoses)[:5])
                ))
            
            # If no clusters were generated, return some default clusters
            if not clusters:
                clusters = [
                    SymptomCluster(
                        clusterName="General Symptoms",
                        symptoms=["Fever", "Fatigue", "Pain", "Headache"],
                        probableDiagnosis="Common conditions requiring evaluation"
                    )
                ]
            
            return clusters
            
        except Exception as error:
            logger.error(f'Error generating symptom clusters: {error}')
            # Return fallback clusters in case of error
            return [
                SymptomCluster(
                    clusterName="Respiratory Symptoms",
                    symptoms=["Cough", "Shortness of breath", "Chest pain", "Fever"],
                    probableDiagnosis="URTI, Bronchitis, Pneumonia"
                ),
                SymptomCluster(
                    clusterName="Cardiovascular Symptoms",
                    symptoms=["Chest pain", "Palpitations", "Leg swelling", "Dizziness"],
                    probableDiagnosis="Heart Failure, Atrial Fibrillation, Hypertension"
                ),
                SymptomCluster(
                    clusterName="Gastrointestinal Symptoms",
                    symptoms=["Abdominal Pain", "Nausea", "Vomiting", "Diarrhea"],
                    probableDiagnosis="Gastroenteritis, IBS, GERD"
                ),
                SymptomCluster(
                    clusterName="Neurological Symptoms",
                    symptoms=["Headache", "Dizziness", "Memory problems", "Blurred vision"],
                    probableDiagnosis="Migraine, Stroke, Hypertension"
                )
            ]
